/* eslint-disable */
// @ts-nocheck
// Generated by scripts/compile-composer-templates.mjs — DO NOT EDIT
import type { CompiledTemplates } from 'diff2html/lib-esm/hoganjs-utils';
import * as Hogan from 'hogan.js';
export const compiledComposerTemplates: CompiledTemplates = {
  "generic-block-header": new Hogan.Template({code: function (c,p,i) { var t=this;t.b(i=i||"");t.b("<tr>");t.b("\n" + i);t.b("    <td class=\"");t.b(t.v(t.f("lineClass",c,p,0)));t.b(" ");t.b(t.v(t.d("CSSLineClass.INFO",c,p,0)));t.b("\"></td>");t.b("\n" + i);t.b("    <td class=\"");t.b(t.v(t.d("CSSLineClass.INFO",c,p,0)));t.b("\">");t.b("\n" + i);t.b("        <div class=\"");t.b(t.v(t.f("contentClass",c,p,0)));t.b("\">");if(t.s(t.f("blockHeader",c,p,1),c,p,0,156,173,"{{ }}")){t.rs(c,p,function(c,p,t){t.b(t.t(t.f("blockHeader",c,p,0)));});c.pop();}if(!t.s(t.f("blockHeader",c,p,1),c,p,1,0,0,"")){t.b("&nbsp;");};t.b("</div>");t.b("\n" + i);t.b("    </td>");t.b("\n" + i);t.b("</tr>");return t.fl(); },partials: {}, subs: {  }}),
  "line-by-line-file-diff": new Hogan.Template({code: function (c,p,i) { var t=this;t.b(i=i||"");t.b("<details open id=\"");t.b(t.v(t.f("fileHtmlId",c,p,0)));t.b("\" class=\"d2h-file-wrapper\" data-lang=\"");t.b(t.v(t.d("file.language",c,p,0)));t.b("\">");t.b("\n" + i);t.b("    <summary class=\"d2h-file-header\">");t.b("\n" + i);t.b("      <code-icon class=\"file-icon--open\" icon=\"chevron-down\"></code-icon>");t.b("\n" + i);t.b("      <code-icon class=\"file-icon--closed\" icon=\"chevron-right\"></code-icon>");t.b("\n" + i);t.b("      ");t.b(t.t(t.f("filePath",c,p,0)));t.b("\n" + i);t.b("    </summary>");t.b("\n" + i);t.b("    <div class=\"d2h-file-diff scrollable\">");t.b("\n" + i);t.b("        <div class=\"d2h-code-wrapper\">");t.b("\n" + i);t.b("            <table class=\"d2h-diff-table\">");t.b("\n" + i);t.b("                <tbody class=\"d2h-diff-tbody\">");t.b("\n" + i);t.b("                ");t.b(t.t(t.f("diffs",c,p,0)));t.b("\n" + i);t.b("                </tbody>");t.b("\n" + i);t.b("            </table>");t.b("\n" + i);t.b("        </div>");t.b("\n" + i);t.b("    </div>");t.b("\n" + i);t.b("</details>");return t.fl(); },partials: {}, subs: {  }}),
  "side-by-side-file-diff": new Hogan.Template({code: function (c,p,i) { var t=this;t.b(i=i||"");t.b("<details id=\"");t.b(t.v(t.f("fileHtmlId",c,p,0)));t.b("\" class=\"d2h-file-wrapper\" data-lang=\"");t.b(t.v(t.d("file.language",c,p,0)));t.b("\">");t.b("\n" + i);t.b("    <summary class=\"d2h-file-header\">");t.b("\n" + i);t.b("      <code-icon class=\"file-icon--open\" icon=\"chevron-down\"></code-icon>");t.b("\n" + i);t.b("      <code-icon class=\"file-icon--closed\" icon=\"chevron-right\"></code-icon>");t.b("\n" + i);t.b("      ");t.b(t.t(t.f("filePath",c,p,0)));t.b("\n" + i);t.b("    </summary>");t.b("\n" + i);t.b("    <div class=\"d2h-files-diff\">");t.b("\n" + i);t.b("        <div class=\"d2h-file-side-diff\">");t.b("\n" + i);t.b("            <div class=\"d2h-code-wrapper\">");t.b("\n" + i);t.b("                <table class=\"d2h-diff-table\">");t.b("\n" + i);t.b("                    <tbody class=\"d2h-diff-tbody\">");t.b("\n" + i);t.b("                    ");t.b(t.t(t.d("diffs.left",c,p,0)));t.b("\n" + i);t.b("                    </tbody>");t.b("\n" + i);t.b("                </table>");t.b("\n" + i);t.b("            </div>");t.b("\n" + i);t.b("        </div>");t.b("\n" + i);t.b("        <div class=\"d2h-file-side-diff\">");t.b("\n" + i);t.b("            <div class=\"d2h-code-wrapper\">");t.b("\n" + i);t.b("                <table class=\"d2h-diff-table\">");t.b("\n" + i);t.b("                    <tbody class=\"d2h-diff-tbody\">");t.b("\n" + i);t.b("                    ");t.b(t.t(t.d("diffs.right",c,p,0)));t.b("\n" + i);t.b("                    </tbody>");t.b("\n" + i);t.b("                </table>");t.b("\n" + i);t.b("            </div>");t.b("\n" + i);t.b("        </div>");t.b("\n" + i);t.b("    </div>");t.b("\n" + i);t.b("</details>");return t.fl(); },partials: {}, subs: {  }}),
  "generic-file-path": new Hogan.Template({code: function (c,p,i) { var t=this;t.b(i=i||"");t.b("<span class=\"d2h-file-name-wrapper\">");t.b("\n" + i);t.b("    <span class=\"d2h-file-name\">");t.b(t.v(t.f("fileDiffName",c,p,0)));t.b("</span>");t.b("\n" + i);t.b(t.rp("<fileTag0",c,p,"    "));t.b("</span>");t.b("\n" + i);t.b("<label class=\"d2h-file-collapse\" hidden>");t.b("\n" + i);t.b("    <input class=\"d2h-file-collapse-input\" type=\"checkbox\" name=\"viewed\" value=\"viewed\">");t.b("\n" + i);t.b("    Viewed");t.b("\n" + i);t.b("</label>");return t.fl(); },partials: {"<fileTag0":{name:"fileTag", partials: {}, subs: {  }}}, subs: {  }})
};
