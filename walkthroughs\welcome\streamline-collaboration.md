### Streamline Collaboration | Access Work Anywhere & Suggest Changes

<a href="command:gitlens.walkthrough.openStreamlineCollaboration" title="Watch the Streamline collaboration tutorial video">
  <img src="./thumbnails/cloud-patches.jpg" alt="Watch the Streamline collaboration tutorial video"/>
</a>

With Cloud Patches, privately and securely share code with specific teammates and other developers, accessible from anywhere. Enhance collaboration without adding noise to your repositories.

With Code Suggest, review pull requests and send suggested changes to teammates in any part of your project, not just the lines of code changed in the PR.

Watch a [video](command:gitlens.walkthrough.openStreamlineCollaboration) about enhancing collaboration with GitLens.
