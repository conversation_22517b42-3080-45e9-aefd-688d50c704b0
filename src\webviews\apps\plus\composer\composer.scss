@use '../../shared/styles/properties';
@use '../../shared/styles/theme';
@use '../../shared/styles/scrollbars';
@use '../../shared/styles/onboarding';
@use '../../shared/styles/utils';

@include utils.dark-theme {
	color-scheme: dark;
	--composer-background-05: var(--color-background--lighten-05);
}
@include utils.light-theme {
	color-scheme: light;
	--composer-background-05: var(--color-background--darken-05);
}

:root {
	--gl-onboarding-width-max: 50rem;
	// --vscode-diffEditor-insertedTextBackground: rgba(156, 204, 44, 0.2);
	// --vscode-diffEditor-removedTextBackground: rgba(255, 0, 0, 0.2);
	// --vscode-diffEditor-insertedLineBackground: rgba(155, 185, 85, 0.2);
	// --vscode-diffEditor-removedLineBackground: rgba(255, 0, 0, 0.2);
	// --vscode-diffEditor-diagonalFill: rgba(204, 204, 204, 0.2);
	// --vscode-diffEditor-unchangedRegionBackground: #252526;
	// --vscode-diffEditor-unchangedRegionForeground: #cccccc;
	// --vscode-diffEditor-unchangedCodeBackground: rgba(116, 116, 116, 0.16);
	// --vscode-multiDiffEditor-headerBackground: #262626;
	// --vscode-multiDiffEditor-background: #1e1e1e;
	// --vscode-multiDiffEditor-border: rgba(204, 204, 204, 0.2);
	--d2h-color: var(--color-foreground);
	--d2h-bg-color: var(--color-background);
	--d2h-border-color: var(--vscode-multiDiffEditor-border);
	--d2h-dim-color: rgba(0, 0, 0, 0.3);
	--d2h-line-border-color: #eee;
	--d2h-file-header-bg-color: var(--vscode-multiDiffEditor-headerBackground, var(--vscode-editor-background));
	--d2h-file-header-border-color: var(--vscode-multiDiffEditor-border);
	--d2h-empty-placeholder-bg-color: #f1f1f1;
	--d2h-empty-placeholder-border-color: #e1e1e1;
	--d2h-selected-color: #c8e1ff;
	--d2h-ins-bg-color: var(--vscode-diffEditor-insertedTextBackground);
	--d2h-ins-border-color: var(--vscode-diffEditor-insertedTextBorder, #b4e2b4);
	--d2h-ins-highlight-bg-color: var(--vscode-diffEditor-insertedTextBackground);
	--d2h-ins-label-color: #399839;
	--d2h-del-bg-color: var(--vscode-diffEditor-removedTextBackground);
	--d2h-del-border-color: var(--vscode-diffEditor-removedTextBorder, #e9aeae);
	--d2h-del-highlight-bg-color: var(--vscode-diffEditor-removedTextBackground);
	--d2h-del-label-color: #c33;
	--d2h-change-del-color: var(--vscode-diffEditor-removedLineBackground);
	--d2h-change-ins-color: var(--vscode-diffEditor-insertedLineBackground);
	--d2h-info-bg-color: #f8fafd;
	--d2h-info-border-color: #d5e4f2;
	--d2h-change-label-color: #d0b44c;
	--d2h-moved-label-color: #3572b0;
	// --d2h-dark-color: #e6edf3;
	// --d2h-dark-bg-color: #0d1117;
	// --d2h-dark-border-color: #30363d;
	// --d2h-dark-dim-color: #6e7681;
	// --d2h-dark-line-border-color: #21262d;
	// --d2h-dark-file-header-bg-color: #161b22;
	// --d2h-dark-file-header-border-color: #30363d;
	// --d2h-dark-empty-placeholder-bg-color: hsla(215, 8%, 47%, 0.1);
	// --d2h-dark-empty-placeholder-border-color: #30363d;
	// --d2h-dark-selected-color: rgba(56, 139, 253, 0.1);
	// --d2h-dark-ins-bg-color: rgba(46, 160, 67, 0.15);
	// --d2h-dark-ins-border-color: rgba(46, 160, 67, 0.4);
	// --d2h-dark-ins-highlight-bg-color: rgba(46, 160, 67, 0.4);
	// --d2h-dark-ins-label-color: #3fb950;
	// --d2h-dark-del-bg-color: rgba(248, 81, 73, 0.1);
	// --d2h-dark-del-border-color: rgba(248, 81, 73, 0.4);
	// --d2h-dark-del-highlight-bg-color: rgba(248, 81, 73, 0.4);
	// --d2h-dark-del-label-color: #f85149;
	// --d2h-dark-change-del-color: rgba(210, 153, 34, 0.2);
	// --d2h-dark-change-ins-color: rgba(46, 160, 67, 0.25);
	// --d2h-dark-info-bg-color: rgba(56, 139, 253, 0.1);
	// --d2h-dark-info-border-color: rgba(56, 139, 253, 0.4);
	// --d2h-dark-change-label-color: #d29922;
	// --d2h-dark-moved-label-color: #3572b0;
}

@include utils.dark-theme {
	// --d2h-color: #e6edf3;
	// --d2h-bg-color: #0d1117;
	// --d2h-border-color: #30363d;
	--d2h-dim-color: #6e7681;
	--d2h-line-border-color: #21262d;
	// --d2h-file-header-bg-color: #161b22;
	// --d2h-file-header-border-color: #30363d;
	--d2h-empty-placeholder-bg-color: hsla(215, 8%, 47%, 0.1);
	--d2h-empty-placeholder-border-color: #30363d;
	--d2h-selected-color: rgba(56, 139, 253, 0.1);
	// --d2h-ins-bg-color: rgba(46, 160, 67, 0.15);
	--d2h-ins-border-color: var(--vscode-diffEditor-insertedTextBorder, rgba(46, 160, 67, 0.4));
	// --d2h-ins-highlight-bg-color: rgba(46, 160, 67, 0.4);
	--d2h-ins-label-color: #3fb950;
	// --d2h-del-bg-color: rgba(248, 81, 73, 0.1);
	--d2h-del-border-color: var(--vscode-diffEditor-removedTextBorder, rgba(248, 81, 73, 0.4));
	// --d2h-del-highlight-bg-color: rgba(248, 81, 73, 0.4);
	--d2h-del-label-color: #f85149;
	// --d2h-change-del-color: rgba(210, 153, 34, 0.2);
	// --d2h-change-ins-color: rgba(46, 160, 67, 0.25);
	--d2h-info-bg-color: rgba(56, 139, 253, 0.1);
	--d2h-info-border-color: rgba(56, 139, 253, 0.4);
	--d2h-change-label-color: #d29922;
	--d2h-moved-label-color: #3572b0;
}

* {
	box-sizing: border-box;
}

// avoids FOUC for elements not yet called with `define()`
:not(:defined) {
	visibility: hidden;
}

[hidden] {
	display: none !important;
}

html {
	height: 100%;
	font-size: 62.5%;
	text-size-adjust: 100%;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

body {
	padding: 0;
	background-color: var(--color-background);
	color: var(--color-foreground);
	font-family: var(--font-family);
	min-height: 100%;
	line-height: 1.4;
	font-size: var(--vscode-font-size);
}

@include scrollbars.scrollbarFix();

@include onboarding.core();
