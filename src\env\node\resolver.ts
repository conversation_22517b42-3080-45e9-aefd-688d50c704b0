import { Uri } from 'vscode';
import { isContainer } from '../../container';
import { isBranch } from '../../git/models/branch';
import { isCommit } from '../../git/models/commit';
import { isRemote } from '../../git/models/remote';
import { isRepository } from '../../git/models/repository';
import { isTag } from '../../git/models/tag';
import { isWorktree } from '../../git/models/worktree';
import { getCancellationTokenId, isCancellationToken } from '../../system/-webview/cancellation';
import { isViewNode } from '../../views/nodes/utils/-webview/node.utils';
import { loggingJsonReplacer } from './json';

export function defaultResolver(...args: unknown[]): string {
	if (args.length === 0) return '';
	if (args.length > 1) return JSON.stringify(args, loggingJsonReplacer);

	const [arg] = args;
	if (arg == null) return '';

	switch (typeof arg) {
		case 'string':
			return arg;

		case 'number':
		case 'boolean':
		case 'undefined':
		case 'symbol':
		case 'bigint':
			return String(arg);

		default:
			if (arg instanceof Error) return String(arg);
			if (arg instanceof Uri) {
				if ('sha' in arg && typeof arg.sha === 'string' && arg.sha) {
					return `${arg.sha}:${arg.toString()}`;
				}
				return arg.toString();
			}
			if (
				isRepository(arg) ||
				isBranch(arg) ||
				isCommit(arg) ||
				isRemote(arg) ||
				isTag(arg) ||
				isWorktree(arg) ||
				isViewNode(arg)
			) {
				return arg.toString();
			}
			if (isContainer(arg)) return '<container>';
			if (isCancellationToken(arg)) return getCancellationTokenId(arg);

			return JSON.stringify(arg, loggingJsonReplacer);
	}
}
