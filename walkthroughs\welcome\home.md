### Start and manage your work in the Home View

<a href="command:gitlens.walkthrough.openHomeViewVideo" title="Watch the Home View tutorial video">
  <img src="./thumbnails/home-view.jpg" alt="Home View"/>
</a>

Located in the VS Code sidebar, the GitLens Home View highlights your current work with clear, actionable insights, guides you to Start Work on your next important tasks, and tracks recently modified branches for easy task-switching.

[Connect Integrations](command:gitlens.walkthrough.connectIntegrations) with hosting and issue services like GitHub, GitLab, Azure DevOps, Jira, Bitbucket, and more to help you monitor and take action on branches, issues, and pull requests.

Learn more on our detailed [Home View Help Center Page](command:gitlens.walkthrough.openHomeViewVideo).
