import type { PromptTemplate } from './models/promptTemplates';

export const generateCommitMessage: PromptTemplate<'generate-commitMessage'> = {
	id: 'generate-commitMessage_v2',
	variables: ['diff', 'context', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with summarizing code changes into a concise but meaningful commit message. You will be provided with a code diff and optional additional context. Your goal is to analyze the changes and create a clear, informative commit message that accurately represents the modifications made to the code

First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

Now, if provided, use this context to understand the motivation behind the changes and any relevant background information:
<~~additional-context~~>
\${context}
</~~additional-context~~>

To create an effective commit message, follow these steps:

1. Carefully analyze the diff and context, focusing on:
   - The purpose and rationale of the changes
   - Any problems addressed or benefits introduced
   - Any significant logic changes or algorithmic improvements
2. Ensure the following when composing the commit message:
   - Emphasize the 'why' of the change, its benefits, or the problem it addresses
   - Use an informal yet professional tone
   - Use a future-oriented manner, third-person singular present tense (e.g., 'Fixes', 'Updates', 'Improves', 'Adds', 'Removes')
   - Be clear and concise
   - Synthesize only meaningful information from the diff and context
   - Avoid outputting code, specific code identifiers, names, or file names unless crucial for understanding
   - Avoid repeating information, broad generalities, and unnecessary phrases like "this", "this commit", or "this change"
3. Summarize the main purpose of the changes in a single, concise sentence, which will be the summary of your commit message
   - Start with a third-person singular present tense verb
   - Limit to 50 characters if possible
4. If necessary, provide a brief explanation of the changes, which will be the body of your commit message
   - Add line breaks for readability and to separate independent ideas
   - Focus on the "why" rather than the "what" of the changes
5. If the changes are related to a specific issue or ticket, include the reference (e.g., "Fixes #123" or "Relates to JIRA-456") at the end of the commit message

Write your commit message summary inside <summary> tags and your commit message body inside <body> tags and include no other text
Example output structure:
<summary>
[commit-message-summary-goes-here]
</summary>
<body>
[commit-message-body-goes-here]
</body>

\${instructions}

Based on the provided code diff and any additional context, create a concise but meaningful commit message following the instructions above`,
};

export const generateCreatePullRequest: PromptTemplate<'generate-create-pullRequest'> = {
	id: 'generate-create-pullRequest_v2',
	variables: ['diff', 'data', 'context', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with summarizing code changes into a concise but meaningful pull request title and description. You will be provided with a code diff and a list of commits. Your goal is to analyze the changes and create a clear, informative title and description that accurately represents the modifications made to the code
First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

Then, review the list of commits to help understand the motivation behind the changes and any relevant background information:
<~~data~~>
\${data}
</~~data~~>

Now, if provided, use this context to understand the motivation behind the changes and any relevant background information:
<~~additional-context~~>
\${context}
</~~additional-context~~>

To create an effective pull request title and description, follow these steps:

1. Carefully analyze the diff, commit messages, context, focusing on:
   - The purpose and rationale of the changes
   - Any problems addressed or benefits introduced
   - Any significant logic changes or algorithmic improvements
2. Ensure the following when composing the pull request title and description:
   - Emphasize the 'why' of the change, its benefits, or the problem it addresses
   - Use an informal yet professional tone
   - Use a future-oriented manner, third-person singular present tense (e.g., 'Fixes', 'Updates', 'Improves', 'Adds', 'Removes')
   - Be clear and concise
   - Synthesize only meaningful information from the diff and context
   - Avoid outputting code, specific code identifiers, names, or file names unless crucial for understanding
   - Avoid repeating information, broad generalities, and unnecessary phrases like "this", "this commit", or "this change"
3. Summarize the main purpose of the changes in a single, concise sentence, which will be the title of your pull request
   - Start with a third-person singular present tense verb
   - Limit to 50 characters if possible
4. Provide a detailed explanation of the changes, which will be the body of your pull request
   - Add line breaks for readability and to separate independent ideas
   - Focus on the "why" rather than the "what" of the changes
   - Structure the body with markdown bullets and headings for clarity
5. If the changes are related to a specific issue or ticket, include the reference (e.g., "Fixes #123" or "Relates to JIRA-456") at the end of the pull request message

Write your title inside <summary> tags and your description inside <body> tags and include no other text
Example output structure:
<summary>
[pull-request-title-goes-here]
</summary>
<body>
[pull-request-body-goes-here]
</body>

\${instructions}

Based on the provided code diff, commit list, and any additional context, create a concise but meaningful pull request title and body following the instructions above`,
};

export const generateStashMessage: PromptTemplate<'generate-stashMessage'> = {
	id: 'generate-stashMessage_v2',
	variables: ['diff', 'context', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with creating a concise but descriptive stash message. You will be provided with a code diff of uncommitted changes. Your goal is to analyze the changes and create a clear, single-line stash message that accurately represents the work in progress being stashed

First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

To create an effective stash message, follow these steps:

1. Analyze the changes and focus on:
   - The primary feature or bug fix was being worked on
   - The overall intent of the changes
   - Any notable file or areas being modified
2. Create a single-line message that:
   - Briefly describes the changes being stashed but must be descriptive enough to identify later
   - Prioritizes the most significant change if multiple changes are present. If multiple related changes are significant, try to summarize them concisely
   - Use a future-oriented manner, third-person singular present tense (e.g., 'Fixes', 'Updates', 'Improves', 'Adds', 'Removes')

Write your stash message inside <summary> tags and include no other text
Example output structure:
<summary>
[stash-message-goes-here]
</summary>

\${instructions}

Based on the provided code diff, create a concise but descriptive stash message following the instructions above`,
};

export const generateCreateCloudPatch: PromptTemplate<'generate-create-cloudPatch'> = {
	id: 'generate-create-cloudPatch_v2',
	variables: ['diff', 'context', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with summarizing code changes into a concise and meaningful title and description. You will be provided with a code diff and optional additional context. Your goal is to analyze the changes and create a clear, informative title and description that accurately represents the modifications made to the code

First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

Now, if provided, use this context to understand the motivation behind the changes and any relevant background information:
<~~additional-context~~>
\${context}
</~~additional-context~~>

To create an effective title and description, follow these steps:

1. Carefully analyze the diff and context, focusing on:
   - The purpose and rationale of the changes
   - Any problems addressed or benefits introduced
   - Any significant logic changes or algorithmic improvements
2. Ensure the following when composing the title and description:
   - Emphasize the 'why' of the change, its benefits, or the problem it addresses
   - Use an informal yet professional tone
   - Use a future-oriented manner, third-person singular present tense (e.g., 'Fixes', 'Updates', 'Improves', 'Adds', 'Removes')
   - Be clear and concise
   - Synthesize only meaningful information from the diff and context
   - Avoid outputting code, specific code identifiers, names, or file names unless crucial for understanding
   - Avoid repeating information, broad generalities, and unnecessary phrases like "this", "this commit", or "this change"
3. Summarize the main purpose of the changes in a single, concise sentence, which will be the title
4. Provide a detailed explanation of the changes, which will be the description
   - Add line breaks for readability and to separate independent ideas
   - Focus on the "why" rather than the "what" of the changes

Write your title inside <summary> tags and your description inside <body> tags and include no other text
Example output structure:
<summary>
[cloud-patch-title-goes-here]
</summary>
<body>
[cloud-patch-description-goes-here]
</body>

\${instructions}

Based on the provided code diff and any additional context, create a concise but meaningful title and description following the instructions above`,
};

export const generateCreateCodeSuggest: PromptTemplate<'generate-create-codeSuggestion'> = {
	id: 'generate-create-codeSuggestion_v2',
	variables: ['diff', 'context', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with summarizing code changes into a concise and meaningful code review title and description. You will be provided with a code diff and optional additional context. Your goal is to analyze the changes and create a clear, informative code review title and description that accurately represents the modifications made to the code

First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

Now, if provided, use this context to understand the motivation behind the changes and any relevant background information:
<~~additional-context~~>
\${context}
</~~additional-context~~>

To create an effective title and description, follow these steps:

1. Carefully analyze the diff and context, focusing on:
   - The purpose and rationale of the changes
   - Any problems addressed or benefits introduced
   - Any significant logic changes or algorithmic improvements
2. Ensure the following when composing the title and description:
   - Emphasize the 'why' of the change, its benefits, or the problem it addresses
   - Use an informal yet professional tone
   - Use a future-oriented manner, third-person singular present tense (e.g., 'Fixes', 'Updates', 'Improves', 'Adds', 'Removes')
   - Be clear and concise
   - Synthesize only meaningful information from the diff and context
   - Avoid outputting code, specific code identifiers, names, or file names unless crucial for understanding
   - Avoid repeating information, broad generalities, and unnecessary phrases like "this", "this commit", or "this change"
3. Summarize the main purpose of the changes in a single, concise sentence, which will be the title
4. Provide a detailed explanation of the changes, which will be the description
   - Add line breaks for readability and to separate independent ideas
   - Focus on the "why" rather than the "what" of the changes

Write your title inside <summary> tags and your description inside <body> tags and include no other text
Example output structure:
<summary>
[code-suggestion-title-goes-here]
</summary>
<body>
[code-suggestion-description-goes-here]
</body>

\${instructions}

Based on the provided code diff and any additional context, create a concise but meaningful code review title and description following the instructions above`,
};

export const explainChanges: PromptTemplate<'explain-changes'> = {
	id: 'explain-changes',
	variables: ['diff', 'message', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with creating clear, technical summaries of code changes that help reviewers understand the modifications and their implications. You will analyze a code diff and the author-provided message to produce a structured summary that captures the essential aspects of the changes

First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

Now, review the author-provided message to help understand the motivation behind the changes and any relevant background information:
<~~message~~>
\${message}
</~~message~~>

Analysis Instructions:
1. Examine the technical changes and their direct implications
2. Consider the scope of changes (small fix vs. major modification)
3. Identify any structural or behavioral changes
4. Look for potential side effects or dependencies
5. Note any obvious testing implications

Write your summary inside <summary> and <body> tags in the following structured markdown format, text in [] brackets should be replaced with your own text, if applicable, not including the brackets:
<summary>
[Concise, one-line description of the change]

[2-3 sentences explaining the core changes and their purpose]
</summary>
<body>
### Changes
- [Key technical modifications]
- [Important structural changes]
- [Modified components/files]

### Impact
- [Behavioral changes]
- [Dependencies affected]
- [Breaking changes, if any]
- [Performance implications, if apparent]
</body>

Guidelines:
- Keep the initial description under 80 characters
- Use clear, technical language
- Focus on observable changes from the diff
- Highlight significant code structure changes
- Base conclusions only on the code diff and message
- Avoid assumptions about business context
- Include specific file/component names only when relevant

\${instructions}

Based on the provided code diff and message, create a focused technical summary following the format above`,
};

export const generateChangelog: PromptTemplate<'generate-changelog'> = {
	id: 'generate-changelog',
	variables: ['data', 'instructions'],
	template: `You are an expert at creating changelogs in the "Keep a Changelog" format (https://keepachangelog.com). Your task is to create a set of clear, informative changelog entries

First, carefully examine the following JSON data containing commit messages and associated issues. The data is structured as an array of "change" objects. Each "change" contains a \`message\` (the commit message) and an \`issues\` array. The \`issues\` array contains objects representing associated issues, each with an \`id\`, \`url\`, and optional \`title\`

<~~data~~>
\${data}
</~~data~~>

Guidelines for creating the changelog:

1. Analyze the commit messages and associated issue titles (if available) to understand the changes made. Be sure to read every commit message and associated issue titles to understand the purpose of each change
2. Group changes into these categories (only include categories with actual changes):
   - Added: New features or capabilities
   - Changed: Changes to existing functionality
   - Deprecated: Features that will be removed in upcoming releases
   - Removed: Features that were removed
   - Fixed: Bug fixes
   - Security: Security-related changes
3. Order entries by importance within each category
4. Write a clear, concise, user-friendly descriptions for each change that focuses on the impact to users
   - Follow the example structure below of the Keep a Changelog format for each entry
   - Start with a third-person singular present tense verb (e.g., "Adds", "Changes", "Improves", "Removes", "Deprecates", "Fixes", etc.)
   - Avoid technical implementation details unless directly relevant to users
   - Combine related changes into single entries when appropriate, grouping the associated issues together as well
   - Focus on the what and why, not the how. One sentence is often sufficient, though bullets can be used for multiple related points
5. Prioritize user-facing changes. If a commit message describes internal refactoring or implementation details, try to infer the user-facing impact (if any) from the issue titles or other commits. If there's no user-facing impact, and no clear external benefit, omit the change
6. Use Markdown headings, links, and bullet points, adhering to Keep a Changelog structure
7. Provide only the changelog entry—no additional text or commentary outside of the changelog

Example output structure:

### Added
- Adds brief description of the added feature ([#Issue-ID](Issue-URL))

### Changed
- Changes brief description of how something changed ([#Issue-ID](Issue-URL))
- Improves brief description of how something improved ([#Issue-ID](Issue-URL))

### Fixed
- Fixes Issue Title or brief description if no title ([#Issue-ID](Issue-URL))

\${instructions}

Based on the provided commit messages and associated issues, create a set of markdown changelog entries following the instructions above. Do not include any explanatory text or metadata`,
};

export const generateSearchQuery: PromptTemplate<'generate-searchQuery'> = {
	id: 'generate-searchQuery_v2',
	variables: ['query', 'date', 'context', 'instructions'],
	template: `You are an advanced AI assistant that converts natural language queries into structured Git search operators. Your task is to analyze a user's natural language query about their Git repository history and convert it into the appropriate search operators.

Available search operators:
- 'message:' - Search in commit messages (e.g. 'message:fix bug'); maps to \`git log --extended-regexp --grep=<value>\`
- 'author:' - Search by a specific author (e.g. 'author:eamodio' or use '@me' for current user); maps to \`git log --author=<value>\`
- 'commit:' - Search by a specific commit SHA (e.g. 'commit:4ce3a')
- 'file:' - Search by file path (e.g. 'file:"package.json"', 'file:"*.ts"'); maps to \`git log -- <value>\`
- 'change:' - Search by specific code changes using regular expressions (e.g. 'change:"function.*auth"', 'change:"import.*react"'); maps to \`git log -G<value>\`
- 'type:' - Search by type -- only stash is currently supported (e.g. 'type:stash')
- 'after:' - Search for commits after a certain date or range (e.g. 'after:2023-01-01', 'after:"6 months ago"', 'after:"last Tuesday"', 'after:"noon"', 'after:"1 month 2 days ago"'); maps to \`git log --since=<value>\`
- 'before:' - Search for commits before a certain date or range (e.g. 'before:2023-01-01', 'before:"6 months ago"', 'before:"yesterday"', 'before:"3PM GMT"'); maps to \`git log --until=<value>\`

File and change values should be double-quoted. You can use multiple message, author, file, and change operators at the same time if needed.

Temporal queries should be converted to appropriate after and/or before operators, leveraging Git's powerful 'approxidate' parser, which understands a wide array of human-centric relative date expressions, including simple terms ("yesterday", "5 minutes ago"), combinations of time units ("1 month 2 days ago"), days of the week ("last Tuesday"), named times ("noon"), and explicit timezones ("3PM GMT").
For specific temporal ranges, e.g. commits made last week, or commits in the last month, use the 'after:' and 'before:' operators with appropriate relative values or calculate absolute dates, using the current date provided below.
For ambiguous time periods like "this week" or "this month", prefer simple relative expressions like "1 week ago" or absolute dates using the current date provided below.

The current date is \${date}
\${context}

User Query: \${query}

\${instructions}

Convert the user's natural language query into the appropriate search operators. Return only the search query string without any explanatory text. If the query cannot be converted to search operators, return the original query as a message search. For complex temporal expressions that might be ambiguous, prefer simpler, more reliable relative date formats.`,
};

export const generateCommits: PromptTemplate<'generate-commits'> = {
	id: 'generate-commits',
	variables: ['hunks', 'existingCommits', 'hunkMap', 'instructions'],
	template: `You are an advanced AI programming assistant tasked with organizing code changes into commits. Your goal is to create a complete set of commits that are related, grouped logically, atomic, and easy to review. You will be working with individual code hunks and may have some existing commits that already have hunks assigned.

First, examine the following JSON array of code hunks that need to be organized:

<hunks>
\${hunks}
</hunks>

Next, examine the following JSON array of existing commits (if any) that already have some hunks assigned:

<existing_commits>
\${existingCommits}
</existing_commits>

Finally, examine the following JSON array which represents a mapping of hunk indices to hunk headers for reference:

<hunk_map>
\${hunkMap}
</hunk_map>

Your task is to create a complete commit organization that includes:
1. All existing commits (unchanged) that already have hunks assigned
2. New commits for any unassigned hunks, organized logically

Follow these guidelines:

1. Preserve all existing commits exactly as they are - do not modify their messages, explanations, or assigned hunks
2. For unassigned hunks, group them into logical units that make sense together and can be applied atomically
3. Use each hunk only once. Ensure all hunks are assigned to exactly one commit
4. Ensure each new commit is self-contained and atomic
5. Write meaningful commit messages that accurately describe the changes in each new commit
6. Provide detailed explanations for new commits
7. Order commits logically (existing commits first, then new commits in dependency order)

Output your complete commit organization as a JSON array. Each commit in the array should be an object with the following properties:
- "message": A string containing the commit message
- "explanation": A string with a detailed explanation of the changes in the commit
- "hunks": An array of objects, each representing a hunk in the commit. Each hunk object should have:
  - "hunk": The hunk index (number) from the hunk_map

Here's an example of the expected JSON structure:

[
  {
    "message": "feat: add user authentication",
    "explanation": "Implements user login and registration functionality with proper validation",
    "hunks": [
      {
        "hunk": 1
      },
      {
        "hunk": 3
      }
    ]
  },
  {
    "message": "fix: handle edge cases in validation",
    "explanation": "Adds proper error handling for invalid input scenarios",
    "hunks": [
      {
        "hunk": 2
      }
    ]
  }
]

Remember:
- Include all existing commits unchanged
- Organize all unassigned hunks into new commits
- Every hunk must be assigned to exactly one commit
- Base your organization on the actual code changes in the hunks

\${instructions}

Now, proceed with your analysis and organization of the commits. Output only the JSON array containing the complete commit organization, and nothing else.
Do not include any preceeding or succeeding text or markup, such as "Here are the commits:" or "Here is a valid JSON array of commits:".
`,
};

export const generateRebase: PromptTemplate<'generate-rebase'> = {
	id: 'generate-rebase',
	variables: ['diff', 'commits', 'data', 'instructions'],
	template: `You are an advanced AI programming assistant tasked with organizing code changes into commits. Your goal is to create a new set of commits that are related, grouped logically, atomic, and easy to review. You will be working with code changes provided in a unified diff format.

First, examine the following unified Git diff of code changes:

<unified_diff>
\${diff}
</unified_diff>

Next, examine the following JSON array which represents a mapping of index to hunk headers in the unified_diff to be used later when mapping hunks to commits:
<hunk_map>
\${data}
</hunk_map>


Your task is to group the hunks in unified_diff into a set of commits, ordered into a commit history as an array. Follow these guidelines:

1. Only organize the hunks themselves, not individual lines within hunks.
2. Group hunks into logical units that make sense together and can be applied atomically.
3. Use each hunk only once. Use all hunks.
4. Ensure each commit is self-contained and only depends on commits that come before it in the new history.
5. Write meaningful commit messages that accurately describe the changes in each commit.
6. Provide a detailed explanation of the changes in each commit.
7. Make sure the new commit history is easy to review and understand.

Output your new commit history as a JSON array. Each commit in the array should be an object representing a grouping of hunks forming that commit, with the following properties:
- "message": A string containing the commit message.
- "explanation": A string with a detailed explanation of the changes in the commit. Write the explanation as if you were explaining the changes to a reviewer who is familiar with the codebase but not the specific changes. Walk through the changes and make references to specific changes where needed, explaining how they achieve the objective of the commit.
- "hunks": An array of objects, each representing a hunk in the commit. Each hunk object should have:
  - "hunk": The hunk index (number) from the hunk_map, matching the equivalent hunk you chose from the unified_diff.

Once you've completed your analysis, generate the JSON output following the specified format.

Here's an example of the expected JSON structure (note that this is just a structural example and does not reflect the actual content you should produce):

[
  {
    "message": "Example commit message",
    "explanation": "Detailed explanation of the changes in this commit",
    "hunks": [
      {
        "hunk": 2
      },
      {
        "hunk": 7
      }
    ]
  }
]

Remember to base your organization of commits solely on the provided unified_diff and hunk_map. Do not introduce any new changes or modify the content of the hunks. Your task is to organize the existing hunks in a logical and reviewable manner.

\${instructions}

Now, proceed with your analysis and organization of the commits. Output only the JSON array containing the commits, and nothing else.
Do not include any preceeding or succeeding text or markup, such as "Here are the commits:" or "Here is a valid JSON array of commits:".
`,
};
